// Wallet authentication utilities
export interface WalletConnection {
  address: string;
  publicKey: string;
  connected: boolean;
}

export interface WalletAdapter {
  name: string;
  url: string;
  icon: string;
  readyState: 'Installed' | 'NotDetected' | 'Loadable' | 'Unsupported';
}

export const SUPPORTED_WALLETS = {
  PHANTOM: 'phantom',
  SOLFLARE: 'solflare',
  BACKPACK: 'backpack',
} as const;