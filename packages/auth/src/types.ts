// Auth types
export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  imageUrl?: string;
  walletAddress?: string;
  tier: 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';
}

export interface AuthSession {
  user: AuthUser;
  accessToken: string;
  refreshToken?: string;
}

export interface AuthProvider {
  signIn: (credentials: any) => Promise<AuthSession>;
  signOut: () => Promise<void>;
  getSession: () => Promise<AuthSession | null>;
}