// Staking utilities
export interface StakingPosition {
  amount: string;
  tier: number;
  startTime: string;
  lastClaimTime: string;
  auraPoints: number;
  isActive: boolean;
}

export interface StakingRewards {
  dailyAura: number;
  totalEarned: number;
  multiplier: number;
}

export const STAKING_TIERS = {
  BRONZE: { minAmount: 1000, multiplier: 1.0, dailyAura: 10 },
  SILVER: { minAmount: 10000, multiplier: 1.5, dailyAura: 25 },
  DIAMOND: { minAmount: 100000, multiplier: 2.0, dailyAura: 50 },
} as const;

export function calculateStakingRewards(position: StakingPosition): StakingRewards {
  const tier = Object.values(STAKING_TIERS)[position.tier] || STAKING_TIERS.BRONZE;
  return {
    dailyAura: tier.dailyAura,
    totalEarned: position.auraPoints,
    multiplier: tier.multiplier,
  };
}