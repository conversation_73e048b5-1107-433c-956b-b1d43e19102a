import { Connection, clusterApiUrl } from '@solana/web3.js';

// Solana connection utilities
export const SOLANA_NETWORKS = {
  MAINNET: 'mainnet-beta',
  DEVNET: 'devnet',
  TESTNET: 'testnet',
} as const;

export type SolanaNetwork = typeof SOLANA_NETWORKS[keyof typeof SOLANA_NETWORKS];

export function createConnection(network: SolanaNetwork = 'devnet'): Connection {
  const endpoint = clusterApiUrl(network);
  return new Connection(endpoint, 'confirmed');
}

export const DEFAULT_CONNECTION = createConnection();