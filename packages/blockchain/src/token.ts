import { PublicKey } from '@solana/web3.js';

// Token definitions and utilities
export const BONKAI_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS || 
  'BonkaiTokenAddressHereReplaceMeWithActualAddress';

export interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
}

export const BONKAI_TOKEN: TokenInfo = {
  mint: BONKAI_TOKEN_ADDRESS,
  name: '<PERSON><PERSON><PERSON>',
  symbol: 'BONKA<PERSON>',
  decimals: 9,
};

export function getTokenMintAddress(): PublicKey {
  return new PublicKey(BONKAI_TOKEN_ADDRESS);
}