// Chat types
export type MessageRole = 'user' | 'assistant' | 'system';
export type ChatVisibility = 'public' | 'private';
export type DocumentKind = 'text' | 'code' | 'image' | 'sheet';

export interface Chat {
  id: string;
  userId: string;
  title: string;
  visibility: ChatVisibility;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  chatId: string;
  role: MessageRole;
  content: string;
  parts: Array<{
    type: string;
    content: string;
  }>;
  attachments: Array<{
    url: string;
    name: string;
    type: string;
    size: number;
  }>;
  model?: string;
  tokenCount?: number;
  createdAt: string;
}

export interface Document {
  id: string;
  userId: string;
  chatId?: string;
  title: string;
  content: string;
  kind: DocumentKind;
  language?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}