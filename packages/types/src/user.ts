// User types
export type UserTier = 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';

export interface User {
  id: string;
  clerkId: string;
  email: string;
  name?: string;
  imageUrl?: string;
  walletAddress?: string;
  walletLinkedAt?: string;
  tier: UserTier;
  tokenBalance: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    telegram: boolean;
  };
}