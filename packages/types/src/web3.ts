// Web3 types
export interface StakingPosition {
  id: string;
  userId: string;
  amount: string;
  tier: number;
  startTime: string;
  lastClaimTime: string;
  auraPoints: number;
  isActive: boolean;
}

export interface TokenUsage {
  id: string;
  userId: string;
  tokens: number;
  model: string;
  timestamp: string;
  month: string;
}

export interface RateLimit {
  id: string;
  userId: string;
  requests: number;
  windowStart: string;
  tier: string;
}

export interface TelegramUser {
  id: string;
  userId: string;
  telegramId: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  languageCode?: string;
  isPremium: boolean;
  createdAt: string;
}

export interface BankaiMove {
  id: string;
  userId: string;
  moveType: string;
  damage: number;
  cost: string;
  timestamp: string;
  txHash?: string;
}