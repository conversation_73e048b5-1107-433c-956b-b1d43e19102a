import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { paginationOptsValidator } from 'convex/server';

// Create a new message
export const createMessage = mutation({
  args: {
    chatId: v.id('chats'),
    role: v.union(
      v.literal('user'),
      v.literal('assistant'),
      v.literal('system'),
    ),
    content: v.string(),
    parts: v.array(
      v.object({
        type: v.string(),
        content: v.string(),
      }),
    ),
    attachments: v.array(
      v.object({
        url: v.string(),
        name: v.string(),
        type: v.string(),
        size: v.number(),
      }),
    ),
    model: v.optional(v.string()),
    tokenCount: v.optional(v.number()),
  },
  returns: v.id('messages'),
  handler: async (ctx, args) => {
    const chat = await ctx.db.get(args.chatId);
    if (!chat) {
      throw new Error('Chat not found');
    }

    const messageId = await ctx.db.insert('messages', {
      chatId: args.chatId,
      role: args.role,
      content: args.content,
      parts: args.parts,
      attachments: args.attachments,
      model: args.model,
      tokenCount: args.tokenCount,
      createdAt: new Date().toISOString(),
    });

    // Update chat's updatedAt timestamp
    await ctx.db.patch(args.chatId, {
      updatedAt: new Date().toISOString(),
    });

    // Track token usage if it's an assistant message
    if (args.role === 'assistant' && args.tokenCount && args.model) {
      const user = await ctx.db.get(chat.userId);
      if (user) {
        const month = new Date().toISOString().slice(0, 7);
        await ctx.db.insert('tokenUsage', {
          userId: chat.userId,
          tokens: args.tokenCount,
          model: args.model,
          timestamp: new Date().toISOString(),
          month,
        });
      }
    }

    return messageId;
  },
});

// Get messages for a chat
export const getChatMessages = query({
  args: {
    chatId: v.id('chats'),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.object({
    page: v.array(v.object({
      _id: v.id('messages'),
      _creationTime: v.number(),
      chatId: v.id('chats'),
      role: v.union(
        v.literal('user'),
        v.literal('assistant'),
        v.literal('system'),
      ),
      content: v.string(),
      parts: v.array(
        v.object({
          type: v.string(),
          content: v.string(),
        }),
      ),
      attachments: v.array(
        v.object({
          url: v.string(),
          name: v.string(),
          type: v.string(),
          size: v.number(),
        }),
      ),
      model: v.optional(v.string()),
      tokenCount: v.optional(v.number()),
      createdAt: v.string(),
      votes: v.object({
        upvotes: v.number(),
        downvotes: v.number(),
        total: v.number(),
      }),
    })),
    isDone: v.boolean(),
    continueCursor: v.union(v.string(), v.null()),
  }),
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query('messages')
      .withIndex('by_chat', (q) => q.eq('chatId', args.chatId))
      .order('asc')
      .paginate(args.paginationOpts);

    // Enrich with vote information
    const enrichedMessages = await Promise.all(
      messages.page.map(async (message) => {
        const votes = await ctx.db
          .query('votes')
          .withIndex('by_message', (q) => q.eq('messageId', message._id))
          .collect();

        const upvotes = votes.filter((v) => v.isUpvoted).length;
        const downvotes = votes.filter((v) => !v.isUpvoted).length;

        return {
          ...message,
          votes: {
            upvotes,
            downvotes,
            total: upvotes - downvotes,
          },
        };
      }),
    );

    return {
      ...messages,
      page: enrichedMessages,
    };
  },
});

// Stream message updates (for real-time streaming)
export const streamMessage = mutation({
  args: {
    chatId: v.id('chats'),
    content: v.string(),
    isComplete: v.boolean(),
    messageId: v.optional(v.id('messages')),
  },
  returns: v.union(v.id('messages'), v.undefined()),
  handler: async (ctx, args) => {
    if (args.messageId) {
      // Update existing message
      await ctx.db.patch(args.messageId, {
        content: args.content,
      });
      return args.messageId;
    } else if (args.isComplete) {
      // Create final message
      return await ctx.db.insert('messages', {
        chatId: args.chatId,
        role: 'assistant',
        content: args.content,
        parts: [{ type: 'text', content: args.content }],
        attachments: [],
        createdAt: new Date().toISOString(),
      });
    }
  },
});

// Vote on a message
export const voteMessage = mutation({
  args: {
    messageId: v.id('messages'),
    userId: v.id('users'),
    isUpvoted: v.boolean(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    // Check if user already voted
    const existingVote = await ctx.db
      .query('votes')
      .withIndex('by_message', (q) => q.eq('messageId', args.messageId))
      .filter((q) => q.eq(q.field('userId'), args.userId))
      .first();

    if (existingVote) {
      // Update existing vote
      await ctx.db.patch(existingVote._id, {
        isUpvoted: args.isUpvoted,
      });
    } else {
      // Create new vote
      await ctx.db.insert('votes', {
        userId: args.userId,
        messageId: args.messageId,
        isUpvoted: args.isUpvoted,
        createdAt: new Date().toISOString(),
      });
    }

    return { success: true };
  },
});

// Remove vote
export const removeVote = mutation({
  args: {
    messageId: v.id('messages'),
    userId: v.id('users'),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const vote = await ctx.db
      .query('votes')
      .withIndex('by_message', (q) => q.eq('messageId', args.messageId))
      .filter((q) => q.eq(q.field('userId'), args.userId))
      .first();

    if (vote) {
      await ctx.db.delete(vote._id);
    }

    return { success: true };
  },
});

// Get message by ID with full details
export const getMessageById = query({
  args: { messageId: v.id('messages') },
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id('messages'),
      _creationTime: v.number(),
      chatId: v.id('chats'),
      role: v.union(
        v.literal('user'),
        v.literal('assistant'),
        v.literal('system'),
      ),
      content: v.string(),
      parts: v.array(
        v.object({
          type: v.string(),
          content: v.string(),
        }),
      ),
      attachments: v.array(
        v.object({
          url: v.string(),
          name: v.string(),
          type: v.string(),
          size: v.number(),
        }),
      ),
      model: v.optional(v.string()),
      tokenCount: v.optional(v.number()),
      createdAt: v.string(),
      votes: v.object({
        upvotes: v.number(),
        downvotes: v.number(),
        total: v.number(),
        details: v.array(v.object({
          _id: v.id('votes'),
          _creationTime: v.number(),
          userId: v.id('users'),
          messageId: v.id('messages'),
          isUpvoted: v.boolean(),
          createdAt: v.string(),
        })),
      }),
    })
  ),
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) return null;

    const votes = await ctx.db
      .query('votes')
      .withIndex('by_message', (q) => q.eq('messageId', args.messageId))
      .collect();

    const upvotes = votes.filter((v) => v.isUpvoted).length;
    const downvotes = votes.filter((v) => !v.isUpvoted).length;

    return {
      ...message,
      votes: {
        upvotes,
        downvotes,
        total: upvotes - downvotes,
        details: votes,
      },
    };
  },
});
