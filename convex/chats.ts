import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { paginationOptsValidator } from 'convex/server';

// Create a new chat
export const createChat = mutation({
  args: {
    userId: v.id('users'),
    title: v.string(),
    visibility: v.union(v.literal('public'), v.literal('private')),
  },
  returns: v.id('chats'),
  handler: async (ctx, args) => {
    const now = new Date().toISOString();

    return await ctx.db.insert('chats', {
      userId: args.userId,
      title: args.title,
      visibility: args.visibility,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get chat by ID
export const getChatById = query({
  args: { chatId: v.id('chats') },
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id('chats'),
      _creationTime: v.number(),
      userId: v.id('users'),
      title: v.string(),
      visibility: v.union(v.literal('public'), v.literal('private')),
      createdAt: v.string(),
      updatedAt: v.string(),
      user: v.union(
        v.null(),
        v.object({
          _id: v.id('users'),
          _creationTime: v.number(),
          clerkId: v.string(),
          email: v.string(),
          name: v.optional(v.string()),
          imageUrl: v.optional(v.string()),
          walletAddress: v.optional(v.string()),
          walletLinkedAt: v.optional(v.string()),
          tier: v.union(
            v.literal('FREE'),
            v.literal('BRONZE'),
            v.literal('SILVER'),
            v.literal('DIAMOND'),
          ),
          tokenBalance: v.number(),
          createdAt: v.string(),
          updatedAt: v.string(),
        })
      ),
      messageCount: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const chat = await ctx.db.get(args.chatId);
    if (!chat) return null;

    const user = await ctx.db.get(chat.userId);
    const messageCount = await ctx.db
      .query('messages')
      .withIndex('by_chat', (q) => q.eq('chatId', args.chatId))
      .collect()
      .then((messages) => messages.length);

    return {
      ...chat,
      user,
      messageCount,
    };
  },
});

// Get chats for user with pagination
export const getUserChats = query({
  args: {
    userId: v.id('users'),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.object({
    page: v.array(v.object({
      _id: v.id('chats'),
      _creationTime: v.number(),
      userId: v.id('users'),
      title: v.string(),
      visibility: v.union(v.literal('public'), v.literal('private')),
      createdAt: v.string(),
      updatedAt: v.string(),
      messageCount: v.number(),
    })),
    isDone: v.boolean(),
    continueCursor: v.union(v.string(), v.null()),
  }),
  handler: async (ctx, args) => {
    const chats = await ctx.db
      .query('chats')
      .withIndex('by_user', (q) => q.eq('userId', args.userId))
      .order('desc')
      .paginate(args.paginationOpts);

    // Enrich with message counts
    const enrichedChats = await Promise.all(
      chats.page.map(async (chat) => {
        const messageCount = await ctx.db
          .query('messages')
          .withIndex('by_chat', (q) => q.eq('chatId', chat._id))
          .collect()
          .then((messages) => messages.length);

        return {
          ...chat,
          messageCount,
        };
      }),
    );

    return {
      ...chats,
      page: enrichedChats,
    };
  },
});

// Update chat title
export const updateChatTitle = mutation({
  args: {
    chatId: v.id('chats'),
    title: v.string(),
  },
  returns: v.undefined(),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.chatId, {
      title: args.title,
      updatedAt: new Date().toISOString(),
    });
  },
});

// Delete chat and all messages
export const deleteChat = mutation({
  args: {
    chatId: v.id('chats'),
    userId: v.id('users'),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const chat = await ctx.db.get(args.chatId);

    if (!chat || chat.userId !== args.userId) {
      throw new Error('Chat not found or unauthorized');
    }

    // Delete all messages in the chat
    const messages = await ctx.db
      .query('messages')
      .withIndex('by_chat', (q) => q.eq('chatId', args.chatId))
      .collect();

    for (const message of messages) {
      // Delete any votes on the message
      const votes = await ctx.db
        .query('votes')
        .withIndex('by_message', (q) => q.eq('messageId', message._id))
        .collect();

      for (const vote of votes) {
        await ctx.db.delete(vote._id);
      }

      await ctx.db.delete(message._id);
    }

    // Delete any documents associated with the chat
    const documents = await ctx.db
      .query('documents')
      .withIndex('by_chat', (q) => q.eq('chatId', args.chatId))
      .collect();

    for (const document of documents) {
      // Delete suggestions for the document
      const suggestions = await ctx.db
        .query('suggestions')
        .withIndex('by_document', (q) => q.eq('documentId', document._id))
        .collect();

      for (const suggestion of suggestions) {
        await ctx.db.delete(suggestion._id);
      }

      await ctx.db.delete(document._id);
    }

    // Delete the chat
    await ctx.db.delete(args.chatId);

    return { success: true };
  },
});

// Get public chats (for discovery)
export const getPublicChats = query({
  args: {
    paginationOpts: paginationOptsValidator,
  },
  returns: v.object({
    page: v.array(v.object({
      _id: v.id('chats'),
      _creationTime: v.number(),
      userId: v.id('users'),
      title: v.string(),
      visibility: v.union(v.literal('public'), v.literal('private')),
      createdAt: v.string(),
      updatedAt: v.string(),
      user: v.union(
        v.null(),
        v.object({
          name: v.optional(v.string()),
          imageUrl: v.optional(v.string()),
        })
      ),
      messageCount: v.number(),
    })),
    isDone: v.boolean(),
    continueCursor: v.union(v.string(), v.null()),
  }),
  handler: async (ctx, args) => {
    const chats = await ctx.db
      .query('chats')
      .withIndex('by_visibility', (q) => q.eq('visibility', 'public'))
      .order('desc')
      .paginate(args.paginationOpts);

    // Enrich with user info and message counts
    const enrichedChats = await Promise.all(
      chats.page.map(async (chat) => {
        const user = await ctx.db.get(chat.userId);
        const messageCount = await ctx.db
          .query('messages')
          .withIndex('by_chat', (q) => q.eq('chatId', chat._id))
          .collect()
          .then((messages) => messages.length);

        return {
          ...chat,
          user: user
            ? {
                name: user.name,
                imageUrl: user.imageUrl,
              }
            : null,
          messageCount,
        };
      }),
    );

    return {
      ...chats,
      page: enrichedChats,
    };
  },
});
