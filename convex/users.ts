import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Doc } from './_generated/dataModel';

// Create or update user from Clerk
export const upsertUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    walletAddress: v.optional(v.string()),
    tier: v.optional(
      v.union(
        v.literal('FREE'),
        v.literal('BRONZE'),
        v.literal('SILVER'),
        v.literal('DIAMOND'),
      ),
    ),
  },
  returns: v.id('users'),
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', args.clerkId))
      .first();

    const now = new Date().toISOString();

    if (existing) {
      const updates: any = {
        email: args.email,
        name: args.name,
        imageUrl: args.imageUrl,
        updatedAt: now,
      };

      if (args.walletAddress !== undefined) {
        updates.walletAddress = args.walletAddress;
        if (args.walletAddress) {
          updates.walletLinkedAt = now;
        }
      }

      if (args.tier !== undefined) {
        updates.tier = args.tier;
      }

      await ctx.db.patch(existing._id, updates);
      return existing._id;
    } else {
      return await ctx.db.insert('users', {
        clerkId: args.clerkId,
        email: args.email,
        name: args.name,
        imageUrl: args.imageUrl,
        walletAddress: args.walletAddress,
        walletLinkedAt: args.walletAddress ? now : undefined,
        tier: args.tier || 'FREE',
        tokenBalance: 0,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Link wallet to user
export const linkWallet = mutation({
  args: {
    clerkId: v.string(),
    walletAddress: v.string(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', args.clerkId))
      .first();

    if (!user) {
      throw new Error('User not found');
    }

    // Check if wallet is already linked to another user
    const existingWallet = await ctx.db
      .query('users')
      .withIndex('by_wallet', (q) => q.eq('walletAddress', args.walletAddress))
      .first();

    if (existingWallet && existingWallet._id !== user._id) {
      throw new Error('Wallet already linked to another account');
    }

    await ctx.db.patch(user._id, {
      walletAddress: args.walletAddress,
      walletLinkedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    return { success: true };
  },
});

// Unlink wallet from user
export const unlinkWallet = mutation({
  args: {
    clerkId: v.string(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', args.clerkId))
      .first();

    if (!user) {
      throw new Error('User not found');
    }

    await ctx.db.patch(user._id, {
      walletAddress: undefined,
      walletLinkedAt: undefined,
      updatedAt: new Date().toISOString(),
    });

    return { success: true };
  },
});

// Get user by Clerk ID (supports "current" for authenticated user)
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id('users'),
      _creationTime: v.number(),
      clerkId: v.string(),
      email: v.string(),
      name: v.optional(v.string()),
      imageUrl: v.optional(v.string()),
      walletAddress: v.optional(v.string()),
      walletLinkedAt: v.optional(v.string()),
      tier: v.union(
        v.literal('FREE'),
        v.literal('BRONZE'),
        v.literal('SILVER'),
        v.literal('DIAMOND'),
      ),
      tokenBalance: v.number(),
      createdAt: v.string(),
      updatedAt: v.string(),
    })
  ),
  handler: async (ctx, args) => {
    // Handle "current" user request
    if (args.clerkId === 'current') {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) return null;

      return await ctx.db
        .query('users')
        .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
        .first();
    }

    return await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', args.clerkId))
      .first();
  },
});

// Update user tier based on token balance
export const updateUserTier = mutation({
  args: {
    userId: v.id('users'),
    tokenBalance: v.number(),
  },
  returns: v.object({
    tier: v.union(
      v.literal('FREE'),
      v.literal('BRONZE'),
      v.literal('SILVER'),
      v.literal('DIAMOND'),
    ),
  }),
  handler: async (ctx, args) => {
    let tier: Doc<'users'>['tier'] = 'FREE';

    if (args.tokenBalance >= 100) {
      tier = 'DIAMOND';
    } else if (args.tokenBalance >= 50) {
      tier = 'SILVER';
    } else if (args.tokenBalance >= 20) {
      tier = 'BRONZE';
    }

    await ctx.db.patch(args.userId, {
      tokenBalance: args.tokenBalance,
      tier,
      updatedAt: new Date().toISOString(),
    });

    return { tier };
  },
});

// Get user stats
export const getUserStats = query({
  args: { userId: v.id('users') },
  returns: v.union(
    v.null(),
    v.object({
      user: v.object({
        _id: v.id('users'),
        _creationTime: v.number(),
        clerkId: v.string(),
        email: v.string(),
        name: v.optional(v.string()),
        imageUrl: v.optional(v.string()),
        walletAddress: v.optional(v.string()),
        walletLinkedAt: v.optional(v.string()),
        tier: v.union(
          v.literal('FREE'),
          v.literal('BRONZE'),
          v.literal('SILVER'),
          v.literal('DIAMOND'),
        ),
        tokenBalance: v.number(),
        createdAt: v.string(),
        updatedAt: v.string(),
      }),
      stats: v.object({
        chatCount: v.number(),
        messageCount: v.number(),
        tokensUsedThisMonth: v.number(),
        tier: v.union(
          v.literal('FREE'),
          v.literal('BRONZE'),
          v.literal('SILVER'),
          v.literal('DIAMOND'),
        ),
        tokenBalance: v.number(),
      }),
    })
  ),
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return null;

    // Get chat count
    const chats = await ctx.db
      .query('chats')
      .withIndex('by_user', (q) => q.eq('userId', args.userId))
      .collect();

    // Get message count
    let messageCount = 0;
    for (const chat of chats) {
      const messages = await ctx.db
        .query('messages')
        .withIndex('by_chat', (q) => q.eq('chatId', chat._id))
        .collect();
      messageCount += messages.length;
    }

    // Get current month token usage
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
    const tokenUsage = await ctx.db
      .query('tokenUsage')
      .withIndex('by_month', (q) => q.eq('month', currentMonth))
      .filter((q) => q.eq(q.field('userId'), args.userId))
      .collect();

    const totalTokensUsed = tokenUsage.reduce(
      (sum, usage) => sum + usage.tokens,
      0,
    );

    return {
      user,
      stats: {
        chatCount: chats.length,
        messageCount,
        tokensUsedThisMonth: totalTokensUsed,
        tier: user.tier,
        tokenBalance: user.tokenBalance,
      },
    };
  },
});
