{"buildCommand": "cd apps/web && bun run build", "outputDirectory": "apps/web/.next", "installCommand": "bun install", "framework": "nextjs", "regions": ["iad1"], "functions": {"apps/web/app/api/**/*.ts": {"maxDuration": 30}, "apps/telegram-bot/src/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/telegram/(.*)", "destination": "/api/telegram/$1"}], "env": {"NODE_ENV": "production"}}