{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "outDir": "dist", "noEmit": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/convex/*": ["../../convex/*"], "@bonkai/ai": ["../../packages/ai/src"], "@bonkai/auth": ["../../packages/auth/src"], "@bonkai/blockchain": ["../../packages/blockchain/src"], "@bonkai/types": ["../../packages/types/src"], "@bonkai/ui": ["../../packages/ui/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}