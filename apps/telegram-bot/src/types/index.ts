import type { ConversationFlavor } from '@grammyjs/conversations';
import type { Context, Api, SessionFlavor } from 'grammy';
import type { Id } from '@/convex/_generated/dataModel';

// Session data structure
export interface SessionData {
  userId?: Id<'users'>;
  step?: 'awaiting_link_code' | 'awaiting_wallet' | 'authenticated';
  linkingData?: {
    clerkId: string;
    code: string;
    expiresAt: number;
  };
  lastActivity?: number;
  tier?: 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';
  tokenUsage?: {
    current: number;
    limit: number;
    resetAt: number;
  };
}

// Custom context type
export type BotContext = Context &
  SessionFlavor<SessionData> &
  ConversationFlavor;

// User tier levels
export enum UserTier {
  FREE = 'FREE',
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  DIAMOND = 'DIAMOND',
}

// Task complexity levels
export enum TaskComplexity {
  SIMPLE = 'SIMPLE',
  MODERATE = 'MODERATE',
  COMPLEX = 'COMPLEX',
}

// Rate limit information
export interface RateLimitInfo {
  allowed: boolean;
  remaining: number;
  resetAt: number;
  tier: UserTier;
}

// Token usage information
export interface TokenUsageInfo {
  used: number;
  limit: number;
  resetAt: number;
  remaining: number;
}

// AI message types
export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  tokenCount?: number;
}

// Linking code information
export interface LinkingCode {
  code: string;
  clerkId: string;
  telegramId: string;
  expiresAt: number;
  used: boolean;
}

// Bot command types
export interface BotCommand {
  command: string;
  description: string;
  tierRequired?: UserTier;
}

// Webhook payload types
export interface WebhookPayload {
  type: 'message' | 'callback_query' | 'inline_query';
  data: any;
  timestamp: number;
}

// Error types
export class BotError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
  ) {
    super(message);
    this.name = 'BotError';
  }
}

export class AuthError extends BotError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTH_ERROR', 401);
  }
}

export class RateLimitError extends BotError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_ERROR', 429);
  }
}

export class TokenLimitError extends BotError {
  constructor(message: string = 'Token limit exceeded') {
    super(message, 'TOKEN_LIMIT_ERROR', 429);
  }
}

export class TierError extends BotError {
  constructor(message: string = 'Insufficient tier access') {
    super(message, 'TIER_ERROR', 403);
  }
}
