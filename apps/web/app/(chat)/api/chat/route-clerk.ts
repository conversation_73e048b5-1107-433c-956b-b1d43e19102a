import {
  convertToModelMessages,
  createUIMessageStream,
  JsonToSseTransformStream,
  smoothStream,
  stepCountIs,
  streamText,
} from 'ai';
import { auth, currentUser } from '@clerk/nextjs/server';
import { type RequestHints, systemPrompt } from '@/lib/ai/prompts';
import {
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import {
  createStreamId,
  getMessagesByChatId,
} from '@/lib/convex/queries';
import type { Id } from '@/convex/_generated/dataModel';
import { convertToUIMessages, generateUUID, getTextFromMessage } from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { isProductionEnvironment } from '@/lib/constants';
import {
  myProvider,
  getModelForTask,
  UserTier,
  TaskComplexity,
  rateLimits,
  trackTokenUsage,
} from '@/lib/ai/providers-openrouter';
import { getUserTier, checkRateLimit } from '@/lib/middleware/token-gate';
import { postRequestBodySchema, type PostRequestBody } from './schema';
import { geolocation } from '@vercel/functions';
import {
  createResumableStreamContext,
  type ResumableStreamContext,
} from 'resumable-stream';
import { after } from 'next/server';
import { ChatSDKError } from '@/lib/errors';
import type { ChatMessage } from '@/lib/types';
import type { ChatModel } from '@/lib/ai/models';
import type { VisibilityType } from '@/components/visibility-selector';

export const maxDuration = 60;

let globalStreamContext: ResumableStreamContext | null = null;

export function getStreamContext() {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
    } catch (error: any) {
      if (error.message.includes('REDIS_URL')) {
        console.log(
          ' > Resumable streams are disabled due to missing REDIS_URL',
        );
      } else {
        console.error(error);
      }
    }
  }

  return globalStreamContext;
}

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    const {
      id,
      message,
      selectedChatModel,
      selectedVisibilityType,
    }: {
      id: string;
      message: ChatMessage;
      selectedChatModel: ChatModel['id'];
      selectedVisibilityType: VisibilityType;
    } = requestBody;

    // Get Clerk auth session
    const { userId } = await auth();

    if (!userId) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // Get user details and tier
    const user = await currentUser();
    const walletAddress = user?.publicMetadata?.walletAddress as
      | string
      | undefined;
    const userTier = await getUserTier(walletAddress);

    // Check rate limits
    const { allowed, remaining } = await checkRateLimit(userId, userTier);

    if (!allowed) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          tier: userTier,
          limit: rateLimits[userTier],
          retryAfter: 3600, // 1 hour
        }),
        { status: 429, headers: { 'Content-Type': 'application/json' } },
      );
    }

    // Determine model based on complexity and tier
    const complexity = analyzeMessageComplexity(message);
    const modelToUse = getModelForTask(complexity, userTier);

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message,
      });

      await saveChat({
        id,
        userId,
        title,
        visibility: selectedVisibilityType,
      });
    } else {
      if (chat.userId !== userId) {
        return new ChatSDKError('forbidden:chat').toResponse();
      }
    }

    const messagesFromDb = await getMessagesByChatId({ chatId: id as Id<'chats'> });
    const uiMessages = [...convertToUIMessages(messagesFromDb), message];

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: 'user',
          parts: message.parts,
          attachments: [],
          createdAt: new Date(),
        },
      ],
    });

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id as Id<'chats'> });

    const stream = createUIMessageStream({
      execute: ({ writer: dataStream }) => {
        const result = streamText({
          model: myProvider.languageModel(modelToUse),
          system: systemPrompt({ selectedChatModel: modelToUse, requestHints }),
          messages: convertToModelMessages(uiMessages),
          stopWhen: stepCountIs(5),
          experimental_activeTools:
            modelToUse === 'chat-model-reasoning'
              ? []
              : [
                  'getWeather',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          tools: {
            getWeather,
            createDocument: createDocument({
              session: {
                user: {
                  id: userId,
                  email: user?.emailAddresses[0]?.emailAddress || '',
                  name: user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.username || '',
                },
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              },
              dataStream,
            }),
            updateDocument: updateDocument({
              session: {
                user: {
                  id: userId,
                  email: user?.emailAddresses[0]?.emailAddress || '',
                  name: user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.username || '',
                },
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              },
              dataStream,
            }),
            requestSuggestions: requestSuggestions({
              session: {
                user: {
                  id: userId,
                  email: user?.emailAddresses[0]?.emailAddress || '',
                  name: user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.username || '',
                },
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              },
              dataStream,
            }),
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
          // Add response headers
          headers: {
            'X-User-Tier': userTier,
            'X-Rate-Limit-Remaining': remaining.toString(),
            'X-Model-Used': modelToUse,
          },
        });

        result.consumeStream();

        dataStream.merge(
          result.toUIMessageStream({
            sendReasoning: true,
          }),
        );
      },
      generateId: generateUUID,
      onFinish: async ({ messages }) => {
        await saveMessages({
          messages: messages.map((message) => ({
            id: message.id,
            role: message.role,
            parts: message.parts,
            createdAt: new Date(),
            attachments: [],
            chatId: id,
          })),
        });
      },
      onError: () => {
        return 'Oops, an error occurred!';
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(
        await streamContext.resumableStream(streamId, () =>
          stream.pipeThrough(new JsonToSseTransformStream()),
        ),
      );
    } else {
      return new Response(stream);
    }
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }

    console.error('Chat API error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const { userId } = await auth();

  if (!userId) {
    return new ChatSDKError('unauthorized:chat').toResponse();
  }

  const chat = await getChatById({ id });

  if (chat.userId !== userId) {
    return new ChatSDKError('forbidden:chat').toResponse();
  }

  const deletedChat = await deleteChatById({ id });

  return Response.json(deletedChat, { status: 200 });
}

// Helper function to analyze message complexity
function analyzeMessageComplexity(message: ChatMessage): TaskComplexity {
  const text = getTextFromMessage(message);
  const wordCount = text.split(/\s+/).length;

  // Simple heuristic based on message length and content
  if (wordCount < 20) {
    return TaskComplexity.SIMPLE;
  } else if (wordCount < 100) {
    return TaskComplexity.MODERATE;
  } else {
    return TaskComplexity.COMPLEX;
  }
}
