import { auth as clerkAuth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

// Type definitions for compatibility with existing code
export type UserType = {
  id: string;
  email: string;
  name: string;
  image?: string;
  walletAddress?: string;
};

export type Session = {
  user: UserType;
  expires?: string;
} | null;

// Auth function that returns a session-like object
export async function auth(): Promise<Session> {
  const { userId } = await clerkAuth();
  
  if (!userId) {
    return null;
  }

  const user = await currentUser();
  
  if (!user) {
    return null;
  }

  return {
    user: {
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress || '',
      name: user.firstName
        ? `${user.firstName} ${user.lastName || ''}`.trim()
        : user.username || '',
      image: user.imageUrl,
      walletAddress: user.publicMetadata?.walletAddress as string | undefined,
    },
  };
}

// Sign in function (Clerk doesn't use this pattern, but for compatibility)
export async function signIn(provider: string, options: any) {
  // This is mainly for compatibility with existing form actions
  // In practice, Clerk handles sign-in through their components
  redirect('/login');
}

// Sign out function
export async function signOut() {
  redirect('/api/auth/signout');
}

// Utility functions
export async function requireAuth(): Promise<UserType> {
  const session = await auth();
  
  if (!session) {
    redirect('/login');
  }
  
  return session.user;
}

export async function getUser(): Promise<UserType | null> {
  const session = await auth();
  return session?.user || null;
}