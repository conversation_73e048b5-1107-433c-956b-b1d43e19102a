import type { Doc, Id } from '@/convex/_generated/dataModel';

// Export Convex document types
export type User = Doc<'users'>;
export type Chat = Doc<'chats'>;
export type Message = Doc<'messages'>;
export type Document = Doc<'documents'>;
export type Vote = Doc<'votes'>;
export type Suggestion = Doc<'suggestions'>;
export type Stream = Doc<'streams'>;
export type StakingPosition = Doc<'stakingPositions'>;
export type TokenUsage = Doc<'tokenUsage'>;
export type RateLimit = Doc<'rateLimits'>;
export type TelegramUser = Doc<'telegramUsers'>;
export type BankaiMove = Doc<'bankaiMoves'>;

// Export ID types
export type UserId = Id<'users'>;
export type ChatId = Id<'chats'>;
export type MessageId = Id<'messages'>;
export type DocumentId = Id<'documents'>;
export type VoteId = Id<'votes'>;
export type SuggestionId = Id<'suggestions'>;
export type StreamId = Id<'streams'>;

// Additional types for compatibility
export type DBMessage = Message;

// User tier types
export type UserTier = 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';

// Message role types
export type MessageRole = 'user' | 'assistant' | 'system';

// Chat visibility types
export type ChatVisibility = 'public' | 'private';

// Document kind types
export type DocumentKind = 'text' | 'code' | 'image' | 'sheet';