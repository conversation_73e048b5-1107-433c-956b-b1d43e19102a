// Temporary stub functions for legacy compatibility
// These should be replaced with Convex queries in the full migration

export async function getUser(email: string): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getUser called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function createGuestUser(): Promise<any[]> {
  // TODO: Replace with Convex mutation
  console.warn(
    'createGuestU<PERSON> called with legacy implementation - needs Convex migration',
  );
  return [
    {
      id: `guest-${Date.now()}`,
      email: null,
      password: null,
    },
  ];
}

export async function saveChat(chat: any): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'saveChat called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function deleteChatById({ id }: { id: string }): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'deleteChatById called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getChatsByUserId({ userId }: { userId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getChatsByUserId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function getChatById({ id }: { id: string }): Promise<any> {
  // TODO: Replace with Convex query
  console.warn(
    'getChatById called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function saveMessages(messages: any): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'saveMessages called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getMessagesByChatId({ chatId }: { chatId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getMessagesByChatId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function voteMessage({ messageId, vote }: { messageId: string; vote: string }): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'voteMessage called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getVotesByChatId({ chatId }: { chatId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getVotesByChatId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function saveDocument(document: any): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'saveDocument called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getDocumentById({ id }: { id: string }): Promise<any> {
  // TODO: Replace with Convex query
  console.warn(
    'getDocumentById called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getDocumentsByUserId({ userId }: { userId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getDocumentsByUserId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function deleteDocumentsByIdAfterTimestamp(): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function saveSuggestions(suggestions: any): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'saveSuggestions called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getSuggestionsByDocumentId({ documentId }: { documentId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getSuggestionsByDocumentId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function createUser(email: string, password: string): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'createUser called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getMessageById({ id }: { id: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getMessageById called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: string;
}): Promise<any> {
  // TODO: Replace with Convex mutation
  console.warn(
    'updateChatVisiblityById called with legacy implementation - needs Convex migration',
  );
  return null;
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getStreamIdsByChatId called with legacy implementation - needs Convex migration',
  );
  return [];
}

export async function getMessageCountByUserId({ userId }: { userId: string }): Promise<number> {
  // TODO: Replace with Convex query
  console.warn(
    'getMessageCountByUserId called with legacy implementation - needs Convex migration',
  );
  return 0;
}

export async function createStreamId(): Promise<string> {
  // TODO: Replace with Convex mutation
  console.warn(
    'createStreamId called with legacy implementation - needs Convex migration',
  );
  return `stream-${Date.now()}`;
}

export async function getDocumentsById({ ids }: { ids: string[] }): Promise<any[]> {
  // TODO: Replace with Convex query
  console.warn(
    'getDocumentsById called with legacy implementation - needs Convex migration',
  );
  return [];
}
