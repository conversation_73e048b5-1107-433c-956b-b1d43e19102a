import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
  openai,
} from 'ai';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { isTestEnvironment } from '../constants';

// OpenRouter configuration
const openrouter = openai({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY || '',
  headers: {
    'HTTP-Referer': process.env.NEXTAUTH_URL || 'https://bonkai.vercel.app',
    'X-Title': 'BonKai Web3 AI',
  },
});

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': openrouter('google/gemini-2.0-flash-exp:free'),
        'chat-model-reasoning': wrapLanguageModel({
          model: openrouter('openai/o3-mini'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': openrouter('google/gemini-2.0-flash-exp:free'),
        'artifact-model': openrouter('google/gemini-2.0-flash-exp:free'),
        'premium-model': openrouter('openai/gpt-4o'),
        'image-model': openrouter('openai/dall-e-3'),
        'video-model': openrouter('google/gemini-pro-vision'),
      },
    });

// Model selection based on user tier
export enum UserTier {
  FREE = 'FREE',
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  DIAMOND = 'DIAMOND',
}

export enum TaskComplexity {
  SIMPLE = 'SIMPLE',
  MODERATE = 'MODERATE',
  COMPLEX = 'COMPLEX',
}

export function getModelForTask(
  complexity: TaskComplexity,
  userTier: UserTier,
  taskType: 'text' | 'image' | 'video' = 'text',
): string {
  // Handle special task types
  if (taskType === 'image') {
    return userTier === UserTier.FREE ? 'chat-model' : 'image-model';
  }

  if (taskType === 'video') {
    return userTier === UserTier.FREE ? 'chat-model' : 'video-model';
  }

  // Text generation based on complexity and tier
  if (userTier === UserTier.FREE) {
    return 'chat-model'; // Always use free tier model
  }

  switch (complexity) {
    case TaskComplexity.SIMPLE:
      return 'chat-model';
    case TaskComplexity.MODERATE:
      return userTier === UserTier.BRONZE ? 'chat-model' : 'premium-model';
    case TaskComplexity.COMPLEX:
      return userTier === UserTier.DIAMOND
        ? 'chat-model-reasoning'
        : 'premium-model';
    default:
      return 'chat-model';
  }
}

// Token limits per tier
export const tokenLimits = {
  [UserTier.FREE]: 10_000, // 10K tokens/month
  [UserTier.BRONZE]: 100_000, // 100K tokens/month
  [UserTier.SILVER]: 500_000, // 500K tokens/month
  [UserTier.DIAMOND]: 2_000_000, // 2M tokens/month
};

// Rate limits per tier (requests per hour)
export const rateLimits = {
  [UserTier.FREE]: 10,
  [UserTier.BRONZE]: 50,
  [UserTier.SILVER]: 200,
  [UserTier.DIAMOND]: 1000,
};

// Token usage tracking function
export async function trackTokenUsage({
  userId,
  model,
  tokens,
}: {
  userId: string;
  model: string;
  tokens: number;
}): Promise<void> {
  // TODO: Implement token usage tracking with Convex
  console.log(`Token usage tracked: ${userId} used ${tokens} tokens with ${model}`);
}
