'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Clock, AlertTriangle, CheckCircle, ArrowUp } from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface RateLimitData {
  tier: UserTier;
  limit: number;
  used: number;
  remaining: number;
  allowed: boolean;
}

interface RateLimitIndicatorProps {
  data: RateLimitData;
  onUpgrade?: () => void;
  compact?: boolean;
  className?: string;
}

const nextTierLimits = {
  FREE: { next: 'BRONZE', limit: 50 },
  BRONZE: { next: 'SILVER', limit: 200 },
  SILVER: { next: 'DIAMOND', limit: 1000 },
  DIAMOND: { next: null, limit: null },
};

export function RateLimitIndicator({
  data,
  onUpgrade,
  compact = false,
  className,
}: RateLimitIndicatorProps) {
  const usagePercentage = (data.used / data.limit) * 100;
  const nextTier = nextTierLimits[data.tier];

  const getStatusColor = () => {
    if (!data.allowed) return 'text-red-600 dark:text-red-400';
    if (usagePercentage >= 80) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getStatusIcon = () => {
    if (!data.allowed) return <AlertTriangle className="size-4" />;
    if (usagePercentage >= 80) return <Clock className="size-4" />;
    return <CheckCircle className="size-4" />;
  };

  const getProgressColor = () => {
    if (!data.allowed) return 'bg-red-500';
    if (usagePercentage >= 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (compact) {
    return (
      <div
        className={cn(
          'flex items-center gap-3 p-3 bg-muted/50 rounded-lg',
          className,
        )}
      >
        <div className={cn('flex items-center gap-1', getStatusColor())}>
          {getStatusIcon()}
          <span className="text-sm font-medium">
            {data.remaining} / {data.limit}
          </span>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-muted-foreground">
              Hourly Requests
            </span>
            <TierBadge tier={data.tier} size="sm" showIcon={false} />
          </div>
          <div className="w-full bg-muted rounded-full h-1.5">
            <div
              className={cn(
                'h-1.5 rounded-full transition-all duration-300',
                getProgressColor(),
              )}
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="size-5" />
              Rate Limit Status
            </CardTitle>
            <CardDescription>
              Hourly request usage and remaining capacity
            </CardDescription>
          </div>
          <TierBadge tier={data.tier} />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className={cn('flex items-center gap-2', getStatusColor())}>
              {getStatusIcon()}
              <span className="font-medium">
                {data.allowed ? 'Requests Available' : 'Rate Limit Exceeded'}
              </span>
            </div>
            <Badge variant={data.allowed ? 'default' : 'destructive'}>
              {data.remaining} remaining
            </Badge>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Usage</span>
              <span className="font-medium">
                {data.used} / {data.limit} requests
              </span>
            </div>
            <Progress value={usagePercentage} className="h-3" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0</span>
              <span>{usagePercentage.toFixed(1)}% used</span>
              <span>{data.limit}</span>
            </div>
          </div>
        </div>

        {/* Status Message */}
        <div
          className={cn(
            'p-4 rounded-lg border',
            data.allowed
              ? usagePercentage >= 80
                ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950/20 dark:border-yellow-800/30'
                : 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800/30'
              : 'bg-red-50 border-red-200 dark:bg-red-950/20 dark:border-red-800/30',
          )}
        >
          <div className="flex items-start gap-2">
            <div className={getStatusColor()}>{getStatusIcon()}</div>
            <div className="space-y-1">
              {!data.allowed ? (
                <>
                  <p className="text-sm font-medium text-red-800 dark:text-red-200">
                    Rate Limit Exceeded
                  </p>
                  <p className="text-xs text-red-700 dark:text-red-300">
                    You&apos;ve reached your hourly request limit. Please wait for
                    the next hour or upgrade your tier for higher limits.
                  </p>
                </>
              ) : usagePercentage >= 80 ? (
                <>
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Approaching Limit
                  </p>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    You&apos;ve used {usagePercentage.toFixed(1)}% of your hourly
                    requests. Consider upgrading for higher limits.
                  </p>
                </>
              ) : (
                <>
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    Sufficient Capacity
                  </p>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    You have {data.remaining} requests remaining in this hour.
                  </p>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Upgrade Prompt */}
        {nextTier.next &&
          onUpgrade &&
          (usagePercentage >= 70 || !data.allowed) && (
            <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Need More Requests?</p>
                  <p className="text-xs text-muted-foreground">
                    Upgrade to {nextTier.next} tier for {nextTier.limit}{' '}
                    requests/hour
                  </p>
                </div>
                <Button size="sm" onClick={onUpgrade}>
                  <ArrowUp className="size-4 mr-1" />
                  Upgrade
                </Button>
              </div>
            </div>
          )}

        {/* Reset Timer */}
        <div className="text-center pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            Rate limits reset every hour on the hour
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
